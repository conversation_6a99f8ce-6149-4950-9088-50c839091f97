import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/cubit/derivative/validate_order/derivative_validate_order_cubit.dart';
import 'package:vp_trading/cubit/derivative_condition_order_edit/derivative_condition_order_edit_cubit.dart';
import 'package:vp_trading/cubit/place_order/available_trade/available_trade_cubit.dart';
import 'package:vp_trading/cubit/place_order/stock_info/stock_info_cubit.dart';
import 'package:vp_trading/model/order/condition_order_book/condition_order_book_model.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/conditional_order_page/edit_dialog/edit_order_conditional_widget.dart';

void showEditConditionalOrderDialog({
  required BuildContext context,
  void Function()? onConfirm,
  void Function(bool)? callBack,
  void Function()? onCancel,
  ConditionOrderBookModel? model,
}) {
  VPPopup.custom(
    padding: const EdgeInsets.all(20),
    child: MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => DerivativeConditionOrderEditCubit()),
        BlocProvider(create: (context) => DerivativeValidateOrderCubit()),
        BlocProvider(
          create:
              (context) =>
                  StockInfoCubit()
                    ..loadData(model?.symbol ?? '', isDerivative: true),
        ),
        BlocProvider(
          create:
              (context) =>
                  AvailableTradeCubit()..getAvailableTrade(
                    accountId:
                        context
                            .read<SubAccountCubit>()
                            .state
                            .derivativeSubAccount
                            .first
                            .id ??
                        '',
                    symbol: model?.symbol ?? '',
                  ),
        ),
      ],
      child: MultiBlocListener(
        listeners: [
          BlocListener<
            DerivativeConditionOrderEditCubit,
            DerivativeConditionOrderEditState
          >(
            listener: (context, state) {
              if (state.status == DerivativeConditionOrderEditStatus.success) {
                showSnackBar(context, 'Sửa lệnh thành công', isSuccess: true);

                callBack?.call(true);
                context.pop();
              } else if (state.status ==
                  DerivativeConditionOrderEditStatus.failure) {
                showSnackBar(
                  context,
                  state.errorMessage ?? 'Có lỗi xảy ra khi sửa lệnh',
                  isSuccess: false,
                );
              }
            },
          ),
          BlocListener<StockInfoCubit, StockInfoState>(
            listener: (context, state) {
              if (state.stockInfo != null) {
                // Update validation cubit with stock info
                final orderAction =
                    model?.side?.toLowerCase() == 'sell'
                        ? OrderAction.sell
                        : OrderAction.buy;

                context.read<DerivativeValidateOrderCubit>().updateParam(
                  stockInfo: state.stockInfo,
                  action: orderAction,
                );
              }
            },
          ),
          BlocListener<AvailableTradeCubit, AvailableTradeState>(
            listener: (context, state) {
              if (state.availableTrade != null) {
                // Update validation cubit with available trade data
                context.read<DerivativeValidateOrderCubit>().updateParam(
                  availableTrade: state.availableTrade,
                );
              }
            },
          ),
        ],
        child: BlocBuilder<
          DerivativeConditionOrderEditCubit,
          DerivativeConditionOrderEditState
        >(
          builder: (context, state) {
            return EditOrderConditionalDialog(model: model);
          },
        ),
      ),
    ),
  ).showDialog(context);
}
